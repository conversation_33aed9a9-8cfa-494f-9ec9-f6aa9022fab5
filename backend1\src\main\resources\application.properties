spring.application.name=backend1
# --- Server ---
server.port=8080

# --- Oracle JDBC (adjust to your DB) ---
spring.datasource.url=*************************************
spring.datasource.username=system
spring.datasource.password=Oracle2022
spring.datasource.driver-class-name=oracle.jdbc.driver.OracleDriver

# --- JPA/Hibernate ---
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.OracleDialect

# --- CORS (we’ll tighten later). Frontend likely runs on 5173 ---
app.cors.allowed-origins=http://localhost:5173

# --- JWT secret (temporary; we’ll move to ENV later) ---
app.jwt.secret=change-this-to-a-long-random-string
app.jwt.expiration-ms=3600000

