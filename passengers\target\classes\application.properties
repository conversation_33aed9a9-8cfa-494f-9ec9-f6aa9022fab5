spring.application.name=passengers

# Database Configuration
spring.datasource.url=*************************************
spring.datasource.username=system
spring.datasource.password=Oracle2022
spring.datasource.driver-class-name=oracle.jdbc.driver.OracleDriver

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# JSON Configuration
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.date-format=yyyy-MM-dd

# Server Configuration
server.port=8082

# Actuator Configuration
management.endpoints.web.exposure.include=health,info

# JWT Configuration (must match backend1 for token validation)
app.jwt.secret=change-this-to-a-long-random-string
app.jwt.expiration-ms=3600000
